import multer from "multer";
import path from "path";
import fs from "fs/promises";
import { fileTypeFromFile } from "file-type";
import * as pdfjsLib from "pdfjs-dist";
import EPub from "epub";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { FileUpload } from "wasp/server/api"; // This type is generated by <PERSON><PERSON> based on the `api` declaration above.
import { Prisma } from "@prisma/client";

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "");

interface Config {
  set(key: string, value: any): Config;
}

interface MulterRequest extends Request {
  file?: Express.Multer.File;
  files?: Express.Multer.File[];
}

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, "/tmp/tp-uploads");
  },
  filename: function (req, file, cb) {
    const originalName = path.parse(file.originalname).name;
    const extension = path.parse(file.originalname).ext;
    const timestamp = Date.now();

    const newFilename = `${originalName}_${timestamp}${extension}`;
    cb(null, newFilename);
  },
});

const upload = multer({ storage: storage });

export const addMiddleware = (config: Config) => {
  config.set("multer", upload.single("file"));

  return config;
};

async function verifyPDF(filePath: string): Promise<boolean> {
  try {
    const data = new Uint8Array(await fs.readFile(filePath));
    const doc = await pdfjsLib.getDocument({ data }).promise;
    await doc.destroy();
    return true;
  } catch (error) {
    console.error("PDF verification failed:", error);
    return false;
  }
}

async function verifyEPUB(filePath: string): Promise<boolean> {
  return new Promise((resolve) => {
    const epub = new (EPub as any)(filePath);

    epub.on("end", () => {
      resolve(true);
    });

    epub.on("error", () => {
      resolve(false);
    });

    epub.parse();
  });
}

const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB limit, matching the client-side limit

async function extractTextFromPDF(filePath: string): Promise<string> {
  const data = new Uint8Array(await fs.readFile(filePath));
  const doc = await pdfjsLib.getDocument({ data }).promise;
  let fullText = "";

  for (let i = 1; i <= doc.numPages; i++) {
    const page = await doc.getPage(i);
    const content = await page.getTextContent();
    const pageText = content.items.map((item: any) => item.str).join(" ");
    fullText += pageText + "\n";
  }

  await doc.destroy();
  return fullText;
}

async function extractTextFromEPUB(filePath: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const epub = new EPub(filePath);
    let fullText = "";

    epub.on("end", () => {
      let chaptersProcessed = 0;
      const totalChapters = epub.flow.length;

      epub.flow.forEach((chapter: any) => {
        epub.getChapter(chapter.id, (error: Error | null, text: string) => {
          if (error) {
            reject(error);
            return;
          }

          // Remove HTML tags
          const cleanText = text.replace(/<[^>]*>/g, " ");
          fullText += cleanText + "\n";

          chaptersProcessed++;
          if (chaptersProcessed === totalChapters) {
            resolve(fullText);
          }
        });
      });
    });

    epub.parse();
  });
}

async function calculateTokenCount(
  text: string
): Promise<{ tokens: number; credits: number }> {
  const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });
  const result = await model.countTokens(text);
  const totalTokens = result.totalTokens;

  // Calculate credits (pages) where 1 credit = 1 page = 500 tokens
  // Round up to ensure partial pages count as full credits
  const credits = Math.ceil(totalTokens / 500);

  return {
    tokens: totalTokens,
    credits: credits,
  };
}

export const uploadFile: FileUpload = async (req, res, context) => {
  try {
    if (!context.user) {
      return res.status(401).json({
        error: "Authentication required",
      });
    }

    const file = req.file;
    if (!file) {
      return res.status(400).json({
        error: "No file uploaded",
      });
    }

    // Verify file size
    if (file.size > MAX_FILE_SIZE) {
      await fs.unlink(file.path);
      return res.status(400).json({
        error: `File size exceeds ${MAX_FILE_SIZE / 1024 / 1024}MB limit`,
      });
    }

    // Verify real file type using file-type
    const fileType = await fileTypeFromFile(file.path);
    if (!fileType) {
      await fs.unlink(file.path);
      return res.status(400).json({
        error: "Could not determine file type",
      });
    }

    // Verify if mime type matches what we expect
    if (
      fileType.mime !== "application/pdf" &&
      fileType.mime !== "application/epub+zip"
    ) {
      await fs.unlink(file.path);
      return res.status(400).json({
        error: "Invalid file type. Only PDF and EPUB files are allowed",
      });
    }

    // Extract text based on file type
    let extractedText = "";
    if (fileType.mime === "application/pdf") {
      const isValid = await verifyPDF(file.path);
      if (!isValid) {
        await fs.unlink(file.path);
        return res.status(400).json({
          error: "PDF file is corrupted or cannot be parsed",
        });
      }
      extractedText = await extractTextFromPDF(file.path);
    } else {
      const isValid = await verifyEPUB(file.path);
      if (!isValid) {
        await fs.unlink(file.path);
        return res.status(400).json({
          error: "EPUB file is corrupted or cannot be parsed",
        });
      }
      extractedText = await extractTextFromEPUB(file.path);
    }

    // Calculate token count and credits
    const { tokens, credits } = await calculateTokenCount(extractedText);
    console.log("Extracted text length:", extractedText.length, "characters");
    console.log("Estimated token count:", tokens);
    console.log("Required credits:", credits);

    if (!(context.user.credits >= credits)) {
      await fs.unlink(file.path);
    }

    const { filename, mimetype } = file;

    const savedFile = await context.entities.File.create({
      data: {
        userId: context.user.id,
        name: filename,
        type: mimetype,
        key: "",
        uploadUrl: "",
      },
    });

    return res.json({
      success: true,
      file: {
        path: file.path,
        originalName: file.originalname,
        mime: fileType.mime,
        characterCount: extractedText.length,
        requiredCredits: credits,
        currentCredits: context.user.credits,
        fileId: savedFile.id,
      },
    });
  } catch (error) {
    console.error("File processing error:", error);
    if (req.file) {
      await fs.unlink(req.file.path).catch(console.error);
    }
    return res.status(500).json({
      error: "File processing failed",
    });
  }
};
