import { Prisma, PrismaClient } from '@prisma/client';
import { ProcessFile } from 'wasp/server/api';
import { MiddlewareConfigFn } from 'wasp/server';
import fs from 'fs/promises';
import path from 'path';
import * as pdfjsLib from 'pdfjs-dist';
import EPub from 'epub';
import { fileTypeFromFile } from 'file-type';
import { GoogleGenerativeAI } from '@google/generative-ai';

const prisma = new PrismaClient();
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');
const OUTPUT_DIR = '/tmp/tp-processed'; // Directory for processed files

// Ensure output directory exists
async function ensureOutputDirExists() {
  try {
    await fs.mkdir(OUTPUT_DIR, { recursive: true });
  } catch (error) {
    console.error('Error creating output directory:', error);
  }
}

// Extract text from PDF
async function extractTextFromPDF(filePath: string): Promise<string> {
  const data = new Uint8Array(await fs.readFile(filePath));
  const doc = await pdfjsLib.getDocument({ data }).promise;
  let fullText = '';

  for (let i = 1; i <= doc.numPages; i++) {
    const page = await doc.getPage(i);
    const content = await page.getTextContent();
    const pageText = content.items.map((item: any) => item.str).join(' ');
    fullText += pageText + '\n';
  }

  await doc.destroy();
  return fullText;
}

// Extract text from EPUB
async function extractTextFromEPUB(filePath: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const epub = new EPub(filePath);
    let fullText = '';

    epub.on('end', () => {
      let chaptersProcessed = 0;
      const totalChapters = epub.flow.length;

      epub.flow.forEach((chapter: any) => {
        epub.getChapter(chapter.id, (error: Error | null, text: string) => {
          if (error) {
            reject(error);
            return;
          }

          // Remove HTML tags
          const cleanText = text.replace(/<[^>]*>/g, ' ');
          fullText += cleanText + '\n';

          chaptersProcessed++;
          if (chaptersProcessed === totalChapters) {
            resolve(fullText);
          }
        });
      });
    });

    epub.parse();
  });
}

// Chunk text to fit within Gemini's token limits
async function chunkText(
  text: string,
  maxTokens: number = 60000
): Promise<string[]> {
  const model = genAI.getGenerativeModel({ model: 'gemini-2.0-flash' });
  const chunks: string[] = [];
  let currentChunk = '';

  // Split text into paragraphs
  const paragraphs = text.split(/\n\s*\n/);

  for (const paragraph of paragraphs) {
    // Check if adding this paragraph would exceed the token limit
    const testChunk = currentChunk + paragraph + '\n\n';
    const { totalTokens } = await model.countTokens(testChunk);

    if (totalTokens > maxTokens && currentChunk.length > 0) {
      // Current chunk is full, save it and start a new one
      chunks.push(currentChunk);
      currentChunk = paragraph + '\n\n';
    } else {
      // Add paragraph to current chunk
      currentChunk = testChunk;
    }
  }

  // Add the last chunk if it's not empty
  if (currentChunk.length > 0) {
    chunks.push(currentChunk);
  }

  return chunks;
}

// Process text with Gemini to simplify the tone
async function simplifyTextWithGemini(
  text: string,
  tone: string = 'down to earth'
): Promise<string> {
  const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });

  const prompt = `You are an expert at rewriting text in a ${tone} tone while preserving all the important information. 
  Rewrite the following text in a ${tone} tone that's easy to understand:`;

  const result = await model.generateContent([prompt, text]);
  return result.response.text();
}

export const apiMiddleware: MiddlewareConfigFn = (config) => {
  return config;
};

export const processFile: ProcessFile = async (req, res, context) => {
  try {
    if (!context.user) {
      return res.status(401).json({
        error: 'Authentication required',
      });
    }

    if (!req.body) {
      return res.status(400).json({
        error: 'No file uploaded',
      });
    }

    const file = await context.entities.File.findUnique({
      where: {
        id: req.body.fileId,
        userId: context.user.id,
      },
    });

    if (!file || file.status !== 'PENDING') {
      return res.status(400).json({
        error: 'Invalid file',
      });
    }

    const result = await prisma.$transaction(
      async (tx: Prisma.TransactionClient) => {
        await tx.file.update({
          where: {
            id: req.body.fileId,
          },
          data: { status: 'PROCESSING' },
        });

        await tx.user.update({
          where: {
            id: context.user!.id,
          },
          data: { credits: { decrement: req.body.requiredCredits } },
        });
      }
    );

    // Start processing in the background
    processFileTask(
      context.entities.File,
      req.body.path,
      req.body.fileId,
      req.body.tone || 'simplified'
    );

    return res.status(200).json({ message: 'Processing started' });
  } catch (error) {
    return res.status(500).json({ message: (error as Error).message });
  }
};

async function processFileTask(
  fileModel: Prisma.FileDelegate,
  filePath: string,
  id: string,
  tone: string
) {
  try {
    console.log('Processing file:', filePath);

    // Ensure output directory exists
    await ensureOutputDirExists();

    // Generate output file path
    const originalFileName = path.basename(filePath, path.extname(filePath));
    const timestamp = Date.now();
    const outputFileName = `${originalFileName}_${tone}_${timestamp}.txt`;
    const outputFilePath = path.join(OUTPUT_DIR, outputFileName);

    // Determine file type
    const fileType = await fileTypeFromFile(filePath);

    // Extract text based on file type
    let extractedText = '';
    if (fileType?.mime === 'application/pdf') {
      extractedText = await extractTextFromPDF(filePath);
    } else if (fileType?.mime === 'application/epub+zip') {
      extractedText = await extractTextFromEPUB(filePath);
    } else {
      throw new Error('Unsupported file type');
    }

    console.log(`Extracted ${extractedText.length} characters of text`);

    // Chunk the text to fit within Gemini's limits
    const chunks = await chunkText(extractedText);
    console.log(`Split text into ${chunks.length} chunks`);

    // Process each chunk with Gemini and write to file
    const outputFileHandle = await fs.open(outputFilePath, 'w');

    try {
      for (let i = 0; i < chunks.length; i++) {
        console.log(`Processing chunk ${i + 1}/${chunks.length}`);
        const processedText = await simplifyTextWithGemini(chunks[i], tone);
        await outputFileHandle.write(processedText + '\n\n');
      }
    } finally {
      await outputFileHandle.close();
    }

    console.log(`Processed text saved to ${outputFilePath}`);

    // Update the file record with the processed file path
    await fileModel.update({
      where: { id },
      data: {
        status: 'COMPLETED',
      },
    });

    console.log(`File ${id} processed successfully`);
  } catch (error) {
    console.error('Error processing file:', (error as Error).message);
    await fileModel.update({
      where: { id },
      data: {
        status: 'FAILED',
      },
    });
  }
}
