import { useAuth } from "wasp/client/auth";
import {
  generateCheckoutSession,
  getCustomerPortalUrl,
  useQuery,
} from "wasp/client/operations";
import { PaymentPlanId, paymentPlans, prettyPaymentPlanName } from "./plans";
import { AiFillCheckCircle } from "react-icons/ai";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { cn } from "../client/cn";

const bestDealPaymentPlanId: PaymentPlanId = PaymentPlanId.Pro;

// Extended plan type that includes both payment and non-payment plans
interface PlanCard {
  id: string;
  name: string;
  price: string;
  description: string;
  features: string[];
  isPaymentPlan: boolean;
  action?: {
    label: string;
    href?: string;
    onClick?: () => void;
  };
  highlight?: boolean;
}

// Create all plan cards including payment and non-payment plans
const createPlanCards = (): PlanCard[] => {
  const planCards: PlanCard[] = [
    // Free Plan
    {
      id: "free",
      name: "Free",
      price: "$0",
      description: "Get started with the basics",
      features: ["100 credits per month", "Basic features"],
      isPaymentPlan: false,
      action: {
        label: "Get Started",
        href: "/signup",
      },
    },
    // Regular payment plans
    ...Object.values(PaymentPlanId)
      .filter((planId) => planId !== PaymentPlanId.PayAsYouGo)
      .map((planId) => {
        return {
          id: planId,
          name: prettyPaymentPlanName(planId),
          price: paymentPlanCards[planId].price,
          description: paymentPlanCards[planId].description,
          features: paymentPlanCards[planId].features,
          isPaymentPlan: true,
          highlight: planId === bestDealPaymentPlanId,
        };
      }),
    // Business Plan
    {
      id: "business",
      name: "Business",
      price: "Custom",
      description: "Enterprise-grade solution for teams",
      features: [
        "Unlimited credits",
        "Dedicated account manager",
        "Priority support",
        "Custom integrations",
        "Admin dashboard",
      ],
      isPaymentPlan: false,
      action: {
        label: "Contact Sales",
        href: "/contact",
      },
    },
  ];

  return planCards;
};

// Original payment plan cards (kept for backward compatibility)
export const paymentPlanCards: Record<
  string,
  {
    name: string;
    price: string;
    description: string;
    features: string[];
  }
> = {
  [PaymentPlanId.Basic]: {
    name: prettyPaymentPlanName(PaymentPlanId.Basic),
    price: "$9.99",
    description: "All you need to get started",
    features: ["2,000 credits per month", "Good support"],
  },
  [PaymentPlanId.Pro]: {
    name: prettyPaymentPlanName(PaymentPlanId.Pro),
    price: "$24.99",
    description: "Our most popular plan",
    features: ["10,000 credits per month", "Priority customer support"],
  },
  [PaymentPlanId.Power]: {
    name: prettyPaymentPlanName(PaymentPlanId.Pro),
    price: "$99.99",
    description: "Good for Nerds",
    features: ["30,000 credits per month", "Priority customer support"],
  },
  [PaymentPlanId.PayAsYouGo]: {
    name: prettyPaymentPlanName(PaymentPlanId.PayAsYouGo),
    price: "$4.99",
    description: "Freedom",
    features: ["250 credits for $4.99"],
  },
};

// PlanCardComponent to represent both payment and non-payment plans
const PlanCardComponent: React.FC<{
  plan: PlanCard;
  isUserSubscribed: boolean;
  isPaymentLoading: boolean;
  handleBuyNowClick: (planId: PaymentPlanId) => void;
  handleCustomerPortalClick: () => void;
}> = ({
  plan,
  isUserSubscribed,
  isPaymentLoading,
  handleBuyNowClick,
  handleCustomerPortalClick,
}) => {
  const navigate = useNavigate();

  const handleActionClick = () => {
    if (plan.action?.href) {
      navigate(plan.action.href);
    } else if (plan.action?.onClick) {
      plan.action.onClick();
    } else if (plan.isPaymentPlan) {
      handleBuyNowClick(plan.id as PaymentPlanId);
    }
  };

  return (
    <div
      className={cn(
        "relative flex flex-col grow justify-between rounded-3xl ring-gray-900/10 dark:ring-gray-100/10 overflow-hidden p-8 xl:p-10",
        {
          "ring-2": plan.highlight,
          "ring-1 lg:mt-8": !plan.highlight,
        }
      )}
    >
      {plan.highlight && (
        <div
          className="absolute top-0 right-0 -z-10 w-full h-full transform-gpu blur-3xl"
          aria-hidden="true"
        >
          <div
            className="absolute w-full h-full bg-gradient-to-br from-amber-400 to-purple-300 opacity-30 dark:opacity-50"
            style={{
              clipPath: "circle(670% at 50% 50%)",
            }}
          />
        </div>
      )}
      <div className="mb-8">
        <div className="flex items-center justify-between gap-x-4">
          <h3
            id={plan.id}
            className="text-gray-900 text-lg font-semibold leading-8 dark:text-white"
          >
            {plan.name}
          </h3>
        </div>
        <p className="mt-4 text-sm leading-6 text-gray-600 dark:text-white">
          {plan.description}
        </p>
        <p className="mt-6 flex items-baseline gap-x-1 dark:text-white">
          <span className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white">
            {plan.price}
          </span>
          <span className="text-sm font-semibold leading-6 text-gray-600 dark:text-white">
            {plan.isPaymentPlan &&
              plan.id !== PaymentPlanId.PayAsYouGo &&
              "/month"}
          </span>
        </p>
        <ul
          role="list"
          className="mt-8 space-y-3 text-sm leading-6 text-gray-600 dark:text-white"
        >
          {plan.features.map((feature) => (
            <li key={feature} className="flex gap-x-3">
              <AiFillCheckCircle
                className="h-6 w-5 flex-none text-yellow-500"
                aria-hidden="true"
              />
              {feature}
            </li>
          ))}
        </ul>
      </div>

      {/* Button for the plan */}
      {plan.isPaymentPlan && isUserSubscribed ? (
        <button
          onClick={handleCustomerPortalClick}
          aria-describedby="manage-subscription"
          className={cn(
            "mt-8 block rounded-md py-2 px-3 text-center text-sm font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-yellow-400",
            {
              "bg-yellow-500 text-white hover:text-white shadow-sm hover:bg-yellow-400":
                plan.highlight,
              "text-gray-600 ring-1 ring-inset ring-purple-200 hover:ring-purple-400":
                !plan.highlight,
            }
          )}
        >
          Manage Subscription
        </button>
      ) : (
        <button
          onClick={handleActionClick}
          aria-describedby={plan.id}
          className={cn(
            {
              "bg-yellow-500 text-white hover:text-white shadow-sm hover:bg-yellow-400":
                plan.highlight,
              "text-gray-600 ring-1 ring-inset ring-purple-200 hover:ring-purple-400":
                !plan.highlight,
            },
            {
              "opacity-50 cursor-wait": plan.isPaymentPlan && isPaymentLoading,
            },
            "mt-8 block rounded-md py-2 px-3 text-center text-sm dark:text-white font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-yellow-400"
          )}
          disabled={
            plan.id === "free" ? true : plan.isPaymentPlan && isPaymentLoading
          }
        >
          {plan.action?.label ||
            (plan.isPaymentPlan ? "Buy plan" : "Get Started")}
        </button>
      )}
    </div>
  );
};

const PricingPage = () => {
  const [isPaymentLoading, setIsPaymentLoading] = useState<boolean>(false);
  const planCards = createPlanCards();

  const { data: user } = useAuth();
  const isUserSubscribed =
    !!user &&
    !!user.subscriptionStatus &&
    user.subscriptionStatus !== "deleted";

  const {
    data: customerPortalUrl,
    isLoading: isCustomerPortalUrlLoading,
    error: customerPortalUrlError,
  } = useQuery(getCustomerPortalUrl, { enabled: isUserSubscribed });

  const navigate = useNavigate();

  async function handleBuyNowClick(paymentPlanId: PaymentPlanId) {
    if (!user) {
      navigate("/login");
      return;
    }
    try {
      setIsPaymentLoading(true);

      const checkoutResults = await generateCheckoutSession(paymentPlanId);

      if (checkoutResults?.sessionUrl) {
        window.open(checkoutResults.sessionUrl, "_self");
      } else {
        throw new Error("Error generating checkout session URL");
      }
    } catch (error) {
      console.error(error);
      setIsPaymentLoading(false); // We only set this to false here and not in the try block because we redirect to the checkout url within the same window
    }
  }

  const handleCustomerPortalClick = () => {
    if (!user) {
      navigate("/login");
      return;
    }

    if (customerPortalUrlError) {
      console.error("Error fetching customer portal url");
    }

    if (!customerPortalUrl) {
      throw new Error(`Customer Portal does not exist for user ${user.id}`);
    }

    window.open(customerPortalUrl, "_blank");
  };

  return (
    <div className="py-10 lg:mt-10">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div id="pricing" className="mx-auto max-w-4xl text-center">
          <h2 className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl dark:text-white">
            Pick your <span className="text-yellow-500">pricing</span>
          </h2>
        </div>
        <p className="mx-auto mt-6 max-w-2xl text-center text-lg leading-8 text-gray-600 dark:text-white">
          Choose between Stripe and LemonSqueezy as your payment provider. Just
          add your Product IDs! Try it out below with test credit card number{" "}
          <br />
          <span className="px-2 py-1 bg-gray-100 rounded-md text-gray-500">
            4242 4242 4242 4242 4242
          </span>
        </p>
        <div className="isolate mx-auto mt-16 grid max-w-md grid-cols-1 gap-y-8 lg:gap-x-8 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3">
          {planCards.map((plan) => (
            <PlanCardComponent
              key={plan.id}
              plan={plan}
              isUserSubscribed={isUserSubscribed}
              isPaymentLoading={isPaymentLoading}
              handleBuyNowClick={handleBuyNowClick}
              handleCustomerPortalClick={handleCustomerPortalClick}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default PricingPage;
