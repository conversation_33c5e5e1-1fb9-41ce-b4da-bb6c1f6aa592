import { useNavigate } from "react-router-dom";
import { cn } from "../../client/cn";
import { useState } from "react";
import { api } from "wasp/client/api";

interface FileInfoDialogProps {
  isOpen: boolean;
  onClose: () => void;
  fileInfo: {
    originalName: string;
    mime: string;
    characterCount: number;
    requiredCredits: number;
    currentCredits: number;
    path: string;
  };
  processingOptions: any;
}

export function FileInfoDialog({
  isOpen,
  onClose,
  fileInfo,
  processingOptions,
}: FileInfoDialogProps) {
  const navigate = useNavigate();
  const hasEnoughCredits = fileInfo.currentCredits >= fileInfo.requiredCredits;
  const creditsNeeded = fileInfo.requiredCredits - fileInfo.currentCredits;
  const [isLoading, setIsLoading] = useState(false);

  const handleConfirm = async () => {
    setIsLoading(true);

    try {
      const response = await api.post(
        "http://localhost:3001/api/process",
        fileInfo
      );

      navigate("/process-book");
    } catch (error) {
      console.error((error as Error).message);
    }
  };

  const handleBuyCredits = () => {
    // Navigate to pricing page
    navigate("/pricing");
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white dark:bg-boxdark rounded-lg shadow-xl w-full max-w-md p-6 mx-4">
        <div className="text-center mb-6">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
            File Information
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mt-1">
            Review your file details before proceeding
          </p>
        </div>

        <div className="space-y-4 mb-6">
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-300">File Name:</span>
            <span className="font-medium text-gray-900 dark:text-white">
              {fileInfo.originalName}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-300">File Type:</span>
            <span className="font-medium text-gray-900 dark:text-white">
              {fileInfo.mime === "application/pdf" ? "PDF" : "EPUB"}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-300">
              character Count:
            </span>
            <span className="font-medium text-gray-900 dark:text-white">
              {fileInfo.characterCount.toLocaleString()}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-300">
              Required Credits:
            </span>
            <span className="font-medium text-gray-900 dark:text-white">
              {fileInfo.requiredCredits}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-300">
              Your Credits:
            </span>
            <span
              className={cn(
                "font-medium",
                hasEnoughCredits
                  ? "text-green-600 dark:text-green-400"
                  : "text-red-600 dark:text-red-400"
              )}
            >
              {fileInfo.currentCredits}
            </span>
          </div>
        </div>

        {!hasEnoughCredits && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <svg
                className="h-5 w-5 text-yellow-500 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                You need {creditsNeeded} more
                {creditsNeeded === 1 ? "credit" : "credits"} to process this
                file.
              </p>
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 dark:text-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md transition-colors duration-200"
          >
            Cancel
          </button>

          {hasEnoughCredits ? (
            <button
              onClick={handleConfirm}
              disabled={isLoading}
              className={cn(
                "px-4 py-2 text-sm font-medium text-white bg-yellow-500 hover:bg-yellow-600 rounded-md transition-colors duration-200",
                isLoading
                  ? "bg-gray-300 cursor-not-allowed"
                  : "bg-yellow-500 hover:bg-yellow-600"
              )}
            >
              {isLoading ? "Loading..." : "Proceed"}
            </button>
          ) : (
            <button
              onClick={handleBuyCredits}
              className="px-4 py-2 text-sm font-medium text-white bg-yellow-500 hover:bg-yellow-600 rounded-md transition-colors duration-200"
            >
              Buy More Credits
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
