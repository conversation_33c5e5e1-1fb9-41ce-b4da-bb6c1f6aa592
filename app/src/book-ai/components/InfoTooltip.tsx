import { useState } from 'react';
import { AiOutlineInfoCircle } from 'react-icons/ai';
import { cn } from '../../client/cn';

interface InfoTooltipProps {
  content: string;
  className?: string;
}

export function InfoTooltip({ content, className }: InfoTooltipProps) {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <div className="relative inline-block">
      <AiOutlineInfoCircle
        className={cn(
          'w-3 h-3 sm:w-4 sm:h-4 text-gray-500 hover:text-gray-700 cursor-help transition-colors duration-200',
          className
        )}
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
      />
      {isVisible && (
        <div className="absolute z-10 w-48 sm:w-64 px-2 sm:px-4 py-2 text-xs sm:text-sm text-white bg-gray-900 rounded-lg shadow-lg dark:bg-gray-800 bottom-full mb-2">
          <div className="relative">
            {content}
            <div className="absolute w-2 h-2 bg-gray-900 dark:bg-gray-800 transform rotate-45 left-[calc(50%-0.25rem)] -bottom-1"></div>
          </div>
        </div>
      )}
    </div>
  );
}
