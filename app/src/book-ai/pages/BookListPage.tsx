import { useEffect, useState } from "react";
import { cn } from "../../client/cn";
import type { BookStatus } from "../../shared/types";

interface FilterProps {
  status: BookStatus | "ALL";
  setStatus: (status: BookStatus | "ALL") => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}

// Static mock data
const mockFiles = [
  {
    id: "1",
    name: "The Great Gatsby",
    type: "PDF",
    status: "COMPLETED" as BookStatus,
    createdAt: new Date("2024-01-15"),
  },
  {
    id: "2",
    name: "1984",
    type: "EPUB",
    status: "PROCESSING" as BookStatus,
    createdAt: new Date("2024-01-16"),
  },
  {
    id: "3",
    name: "To Kill a Mockingbird",
    type: "PDF",
    status: "PENDING" as BookStatus,
    createdAt: new Date("2024-01-17"),
  },
  {
    id: "4",
    name: "Brave New World",
    type: "EPUB",
    status: "FAILED" as BookStatus,
    createdAt: new Date("2024-01-18"),
  },
];

const Filters: React.FC<FilterProps> = ({
  status,
  setStatus,
  searchTerm,
  setSearchTerm,
}) => {
  return (
    <div className="mb-8 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div className="flex items-center gap-4">
        <select
          value={status}
          onChange={(e) => setStatus(e.target.value as BookStatus | "ALL")}
          className="rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm dark:border-gray-600 dark:bg-boxdark"
        >
          <option value="ALL">All Status</option>
          <option value="PENDING">Pending</option>
          <option value="PROCESSING">Processing</option>
          <option value="COMPLETED">Completed</option>
          <option value="FAILED">Failed</option>
        </select>
      </div>
      <div className="relative">
        <input
          type="text"
          placeholder="Search books..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 pl-10 text-sm dark:border-gray-600 dark:bg-boxdark"
        />
        <svg
          className="absolute left-3 top-2.5 h-4 w-4 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </div>
    </div>
  );
};

interface BookCardProps {
  name: string;
  type: string;
  status: BookStatus;
  createdAt: Date;
}

const BookCard: React.FC<BookCardProps> = ({
  name,
  type,
  status,
  createdAt,
}) => {
  const statusColors = {
    COMPLETED: "bg-green-500",
    PROCESSING: "bg-yellow-500",
    FAILED: "bg-red-500",
    PENDING: "bg-blue-500",
  };

  // Processing card state (keep as is)
  if (status === "PROCESSING") {
    return (
      <div className="relative h-[320px] w-[200px] transform overflow-hidden rounded-lg transition-transform duration-300 hover:-translate-y-2">
        {/* Skeleton background */}
        <div className="h-full w-full animate-pulse bg-muted" />

        {/* Shimmer effect */}
        <div className="absolute inset-0">
          <div className="animate-shimmer absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/10 to-transparent" />
        </div>

        {/* Skeleton content */}
        <div className="absolute inset-0 flex flex-col justify-between p-4">
          {/* Status badge - visible and styled */}
          <div className="flex justify-end">
            <div className="flex items-center gap-2 rounded-full bg-yellow-500/20 px-3 py-1">
              <div className="h-2 w-2 rounded-full bg-yellow-500 animate-pulse" />
              <span className="text-xs font-medium text-yellow-500">
                PROCESSING
              </span>
            </div>
          </div>

          {/* Bottom content with visible information */}
          <div className="space-y-3">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white">
              {name}
            </h3>
            <p className="text-sm text-gray-700 dark:text-gray-200">{type}</p>
            <p className="text-xs text-gray-600 dark:text-gray-300">
              Added {createdAt.toLocaleDateString()}
            </p>
            <div className="mt-3 flex gap-2">
              <div className="h-8 flex-1 rounded bg-muted-foreground/20" />
              <div className="h-8 w-20 rounded bg-muted-foreground/20" />{" "}
              {/* Adjusted width for "Details" */}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Failed card state
  if (status === "FAILED") {
    return (
      <div className="group relative h-[320px] w-[200px] transform transition-transform duration-300 hover:-translate-y-2">
        <div className="absolute inset-0 overflow-hidden rounded-lg shadow-lg">
          {/* Grayed out cover image */}
          <img
            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT-sXYE1ZoRZPvjQV_s0xzgNQz2MTgfbtowkQ&s"
            alt={name}
            className="h-full w-full object-cover grayscale"
          />

          {/* Status badge */}
          <div className="absolute right-2 top-2 rounded-full bg-red-500 px-3 py-1 text-xs text-white">
            FAILED
          </div>

          {/* Error overlay (always visible) */}
          <div className="absolute inset-0 flex flex-col justify-between bg-black/60 p-4">
            <div className="flex items-center justify-center h-1/2">
              <svg
                className="h-12 w-12 text-red-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-bold text-white">{name}</h3>
              <p className="text-sm text-gray-200">{type}</p>
              <p className="text-xs text-gray-300">
                Added {createdAt.toLocaleDateString()}
              </p>
              <div className="mt-3 flex gap-2">
                <button className="flex-1 rounded bg-white px-3 py-1 text-sm font-medium text-gray-900 hover:bg-gray-100">
                  Retry
                </button>
                <button className="rounded bg-gray-800 px-3 py-1 text-sm font-medium text-white hover:bg-gray-700">
                  Details
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Pending card state
  if (status === "PENDING") {
    return (
      <div className="group relative h-[320px] w-[200px] transform transition-transform duration-300 hover:-translate-y-2">
        <div className="absolute inset-0 overflow-hidden rounded-lg shadow-lg">
          {/* Dimmed cover image */}
          <img
            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT-sXYE1ZoRZPvjQV_s0xzgNQz2MTgfbtowkQ&s"
            alt={name}
            className="h-full w-full object-cover opacity-50"
          />

          {/* Status badge */}
          <div className="absolute right-2 top-2 rounded-full bg-blue-500 px-3 py-1 text-xs text-white">
            PENDING
          </div>

          {/* Info overlay (always visible) */}
          <div className="absolute inset-0 flex flex-col justify-between bg-gradient-to-t from-black/70 to-transparent p-4">
            <div />
            <div>
              <h3 className="text-lg font-bold text-white">{name}</h3>
              <p className="text-sm text-gray-200">{type}</p>
              <p className="text-xs text-gray-300">
                Added {createdAt.toLocaleDateString()}
              </p>
              <div className="mt-3 flex gap-2">
                <button className="flex-1 rounded bg-white/50 px-3 py-1 text-sm font-medium text-gray-900 cursor-not-allowed">
                  Read
                </button>
                <button className="rounded bg-gray-800 px-3 py-1 text-sm font-medium text-white hover:bg-gray-700">
                  Details
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Completed card state (keep as is)
  return (
    <div className="group relative h-[320px] w-[200px] transform transition-transform duration-300 hover:-translate-y-2">
      {/* Book Card */}
      <div className="absolute inset-0 overflow-hidden rounded-lg shadow-lg">
        {/* Cover image */}
        <img
          src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT-sXYE1ZoRZPvjQV_s0xzgNQz2MTgfbtowkQ&s"
          alt={name}
          className="h-full w-full object-cover"
        />

        {/* Status badge */}
        <div
          className={cn(
            "absolute right-2 top-2 rounded-full px-3 py-1 text-xs text-white",
            statusColors[status]
          )}
        >
          {status}
        </div>

        {/* Book info overlay (visible on hover) */}
        <div className="absolute inset-0 flex flex-col justify-end bg-gradient-to-t from-black/70 to-transparent p-4 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
          <h3 className="text-lg font-bold text-white">{name}</h3>
          <p className="text-sm text-gray-200">{type}</p>
          <p className="text-xs text-gray-300">
            Added {createdAt.toLocaleDateString()}
          </p>

          <div className="mt-3 flex gap-2">
            <button className="flex-1 rounded bg-white px-3 py-1 text-sm font-medium text-gray-900 hover:bg-gray-100">
              Read
            </button>
            <button className="rounded bg-gray-800 px-3 py-1 text-sm font-medium text-white hover:bg-gray-700">
              Details
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function BookListPage() {
  const [status, setStatus] = useState<BookStatus | "ALL">("ALL");
  const [searchTerm, setSearchTerm] = useState("");

  const filteredFiles = mockFiles.filter((file) => {
    const matchesStatus = status === "ALL" || file.status === status;
    const matchesSearch = file.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen  p-8 ">
      <Filters
        status={status}
        setStatus={setStatus}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
      />

      {filteredFiles.length === 0 ? (
        <div className="mt-16 text-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            No books found
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            Try adjusting your filters or upload a new book
          </p>
        </div>
      ) : (
        <div className="mt-8">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
            {filteredFiles.map((file) => (
              <BookCard
                key={file.id}
                name={file.name}
                type={file.type}
                status={file.status}
                createdAt={file.createdAt}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// V2

// import { useState } from "react";
// import { cn } from "../../client/cn";
// import type { BookStatus } from "../../shared/types";

// interface FilterProps {
//   status: BookStatus | "ALL";
//   setStatus: (status: BookStatus | "ALL") => void;
//   searchTerm: string;
//   setSearchTerm: (term: string) => void;
// }

// // Static mock data
// const mockFiles = [
//   {
//     id: "1",
//     name: "The Great Gatsby",
//     type: "PDF",
//     status: "COMPLETED" as BookStatus,
//     createdAt: new Date("2024-01-15"),
//   },
//   {
//     id: "2",
//     name: "1984",
//     type: "EPUB",
//     status: "PROCESSING" as BookStatus,
//     createdAt: new Date("2024-01-16"),
//   },
//   {
//     id: "3",
//     name: "To Kill a Mockingbird",
//     type: "PDF",
//     status: "PENDING" as BookStatus,
//     createdAt: new Date("2024-01-17"),
//   },
//   {
//     id: "4",
//     name: "Brave New World",
//     type: "EPUB",
//     status: "FAILED" as BookStatus,
//     createdAt: new Date("2024-01-18"),
//   },
// ];

// const Filters: React.FC<FilterProps> = ({
//   status,
//   setStatus,
//   searchTerm,
//   setSearchTerm,
// }) => {
//   return (
//     <div className="mb-8 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
//       <div className="flex items-center gap-4">
//         <select
//           value={status}
//           onChange={(e) => setStatus(e.target.value as BookStatus | "ALL")}
//           className="rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm dark:border-gray-600 dark:bg-boxdark"
//         >
//           <option value="ALL">All Status</option>
//           <option value="PENDING">Pending</option>
//           <option value="PROCESSING">Processing</option>
//           <option value="COMPLETED">Completed</option>
//           <option value="FAILED">Failed</option>
//         </select>
//       </div>
//       <div className="relative">
//         <input
//           type="text"
//           placeholder="Search books..."
//           value={searchTerm}
//           onChange={(e) => setSearchTerm(e.target.value)}
//           className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 pl-10 text-sm dark:border-gray-600 dark:bg-boxdark"
//         />
//         <svg
//           className="absolute left-3 top-2.5 h-4 w-4 text-gray-400"
//           fill="none"
//           stroke="currentColor"
//           viewBox="0 0 24 24"
//         >
//           <path
//             strokeLinecap="round"
//             strokeLinejoin="round"
//             strokeWidth="2"
//             d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
//           />
//         </svg>
//       </div>
//     </div>
//   );
// };

// interface BookCardProps {
//   name: string;
//   type: string;
//   status: BookStatus;
//   createdAt: Date;
//   coverUrl?: string;
//   progress?: number;
//   summary?: string;
// }

// const BookCard: React.FC<BookCardProps> = ({
//   name,
//   type,
//   status,
//   createdAt,
//   coverUrl,
//   progress = 0,
//   summary = "No summary available",
// }) => {
//   const statusConfig = {
//     COMPLETED: {
//       color: "bg-emerald-500/10",
//       textColor: "text-emerald-500",
//       borderColor: "border-emerald-500/20",
//       icon: "✓",
//       label: "Ready",
//     },
//     PROCESSING: {
//       color: "bg-amber-500/10",
//       textColor: "text-amber-500",
//       borderColor: "border-amber-500/20",
//       icon: "↻",
//       label: "Processing",
//     },
//     FAILED: {
//       color: "bg-red-500/10",
//       textColor: "text-red-500",
//       borderColor: "border-red-500/20",
//       icon: "✕",
//       label: "Failed",
//     },
//     PENDING: {
//       color: "bg-blue-500/10",
//       textColor: "text-blue-500",
//       borderColor: "border-blue-500/20",
//       icon: "⋯",
//       label: "Pending",
//     },
//   };

//   const currentStatus = statusConfig[status];

//   return (
//     <div className="group relative">
//       {/* Card Container */}
//       <div
//         className={cn(
//           "relative overflow-hidden rounded-2xl border transition-all duration-300",
//           "bg-white dark:bg-gray-800",
//           "hover:shadow-lg dark:hover:shadow-gray-700/30",
//           currentStatus.borderColor
//         )}
//       >
//         {/* Top Section */}
//         <div className="relative p-6">
//           {/* Status & Type */}
//           <div className="mb-6 flex items-center justify-between">
//             <div
//               className={cn(
//                 "rounded-full px-3 py-1",
//                 currentStatus.color,
//                 currentStatus.textColor,
//                 "text-xs font-medium"
//               )}
//             >
//               {currentStatus.label}
//             </div>
//             <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
//               {type.toUpperCase()}
//             </span>
//           </div>

//           {/* Book Title & Date */}
//           <div className="mb-4">
//             <h3 className="mb-1 text-xl font-bold text-gray-900 dark:text-white">
//               {name}
//             </h3>
//             <p className="text-sm text-gray-500 dark:text-gray-400">
//               Added {new Date(createdAt).toLocaleDateString()}
//             </p>
//           </div>

//           {/* Progress Section */}
//           {status === "PROCESSING" && (
//             <div className="mb-4">
//               <div className="mb-2 flex items-center justify-between text-sm">
//                 <span className="font-medium text-gray-700 dark:text-gray-300">
//                   Processing
//                 </span>
//                 <span className="font-medium text-gray-700 dark:text-gray-300">
//                   {progress}%
//                 </span>
//               </div>
//               <div className="h-1.5 w-full overflow-hidden rounded-full bg-gray-100 dark:bg-gray-700">
//                 <div
//                   className="h-full rounded-full bg-blue-500 transition-all duration-300"
//                   style={{ width: `${progress}%` }}
//                 />
//               </div>
//             </div>
//           )}

//           {/* Summary Preview */}
//           <p className="line-clamp-2 text-sm text-gray-600 dark:text-gray-300">
//             {summary}
//           </p>
//         </div>

//         {/* Bottom Action Bar */}
//         <div
//           className={cn(
//             "flex items-center justify-between border-t p-4",
//             "bg-gray-50 dark:bg-gray-800/50",
//             "border-gray-100 dark:border-gray-700"
//           )}
//         >
//           <div className="flex items-center gap-3">
//             <button
//               className={cn(
//                 "flex items-center gap-2 rounded-lg px-4 py-2",
//                 "bg-blue-500 text-sm font-medium text-white",
//                 "hover:bg-blue-600 transition-colors duration-200"
//               )}
//             >
//               Read Now
//               <svg
//                 className="h-4 w-4"
//                 fill="none"
//                 viewBox="0 0 24 24"
//                 stroke="currentColor"
//               >
//                 <path
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   strokeWidth={2}
//                   d="M14 5l7 7m0 0l-7 7m7-7H3"
//                 />
//               </svg>
//             </button>
//             <button
//               className={cn(
//                 "flex items-center gap-2 rounded-lg px-4 py-2",
//                 "text-sm font-medium text-gray-700 dark:text-gray-300",
//                 "hover:bg-gray-100 dark:hover:bg-gray-700/50",
//                 "transition-colors duration-200"
//               )}
//             >
//               Share
//               <svg
//                 className="h-4 w-4"
//                 fill="none"
//                 viewBox="0 0 24 24"
//                 stroke="currentColor"
//               >
//                 <path
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   strokeWidth={2}
//                   d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
//                 />
//               </svg>
//             </button>
//           </div>

//           {/* Cover Image Preview */}
//           <div className="relative h-12 w-12 overflow-hidden rounded-lg">
//             {coverUrl ? (
//               <img
//                 src={coverUrl}
//                 alt={name}
//                 className="h-full w-full object-cover"
//               />
//             ) : (
//               <div className="flex h-full items-center justify-center bg-gray-100 dark:bg-gray-700">
//                 <svg
//                   className="h-6 w-6 text-gray-400"
//                   fill="none"
//                   viewBox="0 0 24 24"
//                   stroke="currentColor"
//                 >
//                   <path
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                     strokeWidth={2}
//                     d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
//                   />
//                 </svg>
//               </div>
//             )}
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// // Add these CSS classes to your global styles
// const globalStyles = `
//   .perspective-1000 {
//     perspective: 1000px;
//   }

//   .transform-3d {
//     transform-style: preserve-3d;
//   }

//   .rotate-y-10 {
//     transform: rotateY(10deg);
//   }

//   .vertical-text {
//     writing-mode: vertical-rl;
//     text-orientation: mixed;
//   }

//   .book-spine {
//     transform: translateZ(15px);
//   }

//   .book-cover {
//     transform: translateZ(10px);
//   }

//   .book-pages {
//     transform: translateZ(5px);
//   }
// `;

// export default function BookListPage() {
//   const [status, setStatus] = useState<BookStatus | "ALL">("ALL");
//   const [searchTerm, setSearchTerm] = useState("");

//   const filteredFiles = mockFiles.filter((file) => {
//     const matchesStatus = status === "ALL" || file.status === status;
//     const matchesSearch = file.name
//       .toLowerCase()
//       .includes(searchTerm.toLowerCase());
//     return matchesStatus && matchesSearch;
//   });

//   return (
//     <div className="py-6 sm:py-10">
//       <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
//         <div className="mb-8 sm:flex sm:items-center sm:justify-between">
//           <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
//             Your <span className="text-yellow-500">Books</span>
//           </h1>
//           <p className="mt-2 text-sm text-gray-700 dark:text-gray-300 sm:mt-0">
//             {filteredFiles.length} book
//             {filteredFiles.length !== 1 ? "s" : ""} found
//           </p>
//         </div>

//         <Filters
//           status={status}
//           setStatus={setStatus}
//           searchTerm={searchTerm}
//           setSearchTerm={setSearchTerm}
//         />

//         {filteredFiles.length === 0 ? (
//           <div className="mt-16 text-center">
//             <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
//               No books found
//             </h3>
//             <p className="text-gray-500 dark:text-gray-400">
//               Try adjusting your filters or upload a new book
//             </p>
//           </div>
//         ) : (
//           <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
//             {filteredFiles.map((file) => (
//               <BookCard
//                 key={file.id}
//                 name={file.name}
//                 type={file.type}
//                 status={file.status}
//                 createdAt={file.createdAt}
//               />
//             ))}
//           </div>
//         )}
//       </div>
//     </div>
//   );
// }
