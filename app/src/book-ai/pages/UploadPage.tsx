import { useState } from "react";
import { cn } from "../../client/cn";
import { InfoTooltip } from "../components/InfoTooltip";
import { FileInfoDialog } from "../components/FileInfoDialog";
import { api } from "wasp/client/api";

type Action = "paraphrase" | "translate" | "summarize";

type Tone =
  | "academic"
  | "simplified"
  | "kindergarten"
  | "poetic"
  | "humorous"
  | "philosophical"
  | "sarcastic";

interface ProcessingOptions {
  action: Action;
  tone?: Tone; // Only used when action is 'paraphrase'
}

const ALLOWED_FILE_TYPES = [
  "application/pdf",
  "application/epub+zip", // for .epub files
];

const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB limit

const ACTIONS: { value: Action; label: string }[] = [
  { value: "paraphrase", label: "Paraphrase" },
  { value: "translate", label: "Translate" },
  { value: "summarize", label: "Summarize" },
];

const TONES: { value: Tone; label: string }[] = [
  { value: "academic", label: "Academic" },
  { value: "simplified", label: "Simplified" },
  { value: "kindergarten", label: "Kindergarten" },
  { value: "poetic", label: "Poetic" },
  { value: "humorous", label: "Humorous" },
  { value: "philosophical", label: "Philosophical" },
  { value: "sarcastic", label: "Sarcastic" },
];

interface UploadError {
  message: string;
  code:
    | "NO_FILE"
    | "INVALID_FILE_TYPE"
    | "FILE_TOO_LARGE"
    | "NO_OPTIONS_SELECTED"
    | "UPLOAD_FAILED";
}

export default function UploadPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadError, setUploadError] = useState<UploadError | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [processingOptions, setProcessingOptions] = useState<ProcessingOptions>(
    {
      action: "paraphrase",
      tone: undefined,
    }
  );
  const [isUploading, setIsUploading] = useState(false);
  const [showFileInfoDialog, setShowFileInfoDialog] = useState(false);
  const [fileInfo, setFileInfo] = useState<any>(null);

  const validateFile = (file: File): UploadError | null => {
    if (file.size > MAX_FILE_SIZE) {
      return {
        message: `File size exceeds ${MAX_FILE_SIZE / 1024 / 1024}MB limit.`,
        code: "FILE_TOO_LARGE",
      };
    }
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return {
        message: `File type '${file.type}' is not supported. Please upload PDF or EPUB files.`,
        code: "INVALID_FILE_TYPE",
      };
    }
    return null;
  };

  const handleFileSelect = (file: File) => {
    const error = validateFile(file);
    if (error) {
      setUploadError(error);
      setSelectedFile(null);
      return;
    }
    setUploadError(null);
    setSelectedFile(file);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const file = e.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedFile) {
      setUploadError({
        message: "Please select a file to upload.",
        code: "NO_FILE",
      });
      return;
    }

    if (processingOptions.action === "paraphrase" && !processingOptions.tone) {
      setUploadError({
        message: "Please select a tone for paraphrasing.",
        code: "NO_OPTIONS_SELECTED",
      });
      return;
    }

    try {
      setIsUploading(true);
      const formData = new FormData();
      formData.append("file", selectedFile);

      const response = await api.post(
        "http://localhost:3001/api/upload",
        formData
      );

      if (response.data.error) {
        setUploadError({
          message: response.data.error,
          code: "UPLOAD_FAILED",
        });
        return;
      }

      // Show file info dialog instead of navigating directly
      setFileInfo(response.data.file);
      setShowFileInfoDialog(true);
    } catch (error: any) {
      setUploadError({
        message: error.response?.data?.error || "Failed to upload file",
        code: "UPLOAD_FAILED",
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="py-6 sm:py-10 lg:mt-10">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-5xl dark:text-white">
            Process Your <span className="text-yellow-500">Book</span>
          </h2>
        </div>
        <p className="mx-auto mt-4 sm:mt-6 max-w-2xl text-center text-base sm:text-lg leading-7 sm:leading-8 text-gray-600 dark:text-white">
          Upload your book in PDF or EPUB format. Maximum file size is 50MB.
        </p>

        <div className="mt-6 sm:mt-10 flex justify-center">
          <div className="w-full max-w-xl">
            <form
              onSubmit={handleSubmit}
              className="flex flex-col items-center space-y-6 sm:space-y-8"
            >
              {/* Action Selection */}
              <div className="w-full">
                <div className="flex items-center gap-1 sm:gap-2 mb-2">
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-200">
                    Actions
                  </label>
                  <InfoTooltip content="Select how you want to process your text. Each action provides different results." />
                </div>
                <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 gap-2 sm:gap-4">
                  {ACTIONS.map(({ value, label }) => (
                    <div key={value}>
                      <button
                        type="button"
                        onClick={() =>
                          setProcessingOptions((prev) => ({
                            action: value,
                            tone:
                              value === "paraphrase" ? prev.tone : undefined,
                          }))
                        }
                        className={cn(
                          "w-full py-1.5 sm:py-2 px-2 sm:px-4 rounded-md text-sm sm:text-base font-medium transition-colors duration-200",
                          processingOptions.action === value
                            ? "bg-yellow-500 text-white"
                            : "bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-100"
                        )}
                      >
                        {label}
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Tone Selection (only for paraphrase) */}
              {processingOptions.action === "paraphrase" && (
                <div className="w-full">
                  <div className="flex items-center gap-1 sm:gap-2 mb-2">
                    <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-200">
                      Tones
                    </label>
                    <InfoTooltip content="Choose the writing style that best suits your needs. Each tone provides a unique way of expressing the content." />
                  </div>
                  <div className="grid grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 gap-2 sm:gap-4">
                    {TONES.map(({ value, label }) => (
                      <div key={value}>
                        <button
                          type="button"
                          onClick={() =>
                            setProcessingOptions((prev) => ({
                              ...prev,
                              tone: value,
                            }))
                          }
                          className={cn(
                            "w-full py-1.5 sm:py-2 px-2 sm:px-4 rounded-md text-sm sm:text-base font-medium transition-colors duration-200",
                            processingOptions.tone === value
                              ? "bg-yellow-500 text-white"
                              : "bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-100"
                          )}
                        >
                          {label}
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* File Upload Section */}
              <div
                className={cn(
                  "w-full h-64 border-2 border-dashed rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer",
                  isDragging
                    ? "border-yellow-500 bg-yellow-50"
                    : "border-gray-300",
                  "transition-colors duration-200"
                )}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={() => document.getElementById("file-upload")?.click()}
              >
                <input
                  type="file"
                  id="file-upload"
                  className="hidden"
                  accept={ALLOWED_FILE_TYPES.join(",")}
                  onChange={handleFileInput}
                />
                <div className="text-center">
                  {selectedFile ? (
                    <>
                      <p className="text-lg font-semibold text-gray-900 dark:text-white">
                        {selectedFile.name}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {(selectedFile.size / 1024 / 1024).toFixed(2)}MB
                      </p>
                    </>
                  ) : (
                    <>
                      <p className="text-lg font-semibold text-gray-900 dark:text-white">
                        Drag and drop your book here
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        or click to select a file
                      </p>
                    </>
                  )}
                </div>
              </div>

              {uploadError && (
                <div className="mt-4 text-red-500 text-sm">
                  {uploadError.message}
                </div>
              )}

              <button
                type="submit"
                disabled={
                  !selectedFile ||
                  isUploading ||
                  (processingOptions.action === "paraphrase" &&
                    !processingOptions.tone)
                }
                className={cn(
                  "px-8 py-3 rounded-md font-semibold text-white",
                  !selectedFile ||
                    isUploading ||
                    (processingOptions.action === "paraphrase" &&
                      !processingOptions.tone)
                    ? "bg-gray-300 cursor-not-allowed"
                    : "bg-yellow-500 hover:bg-yellow-600",
                  "transition-colors duration-200"
                )}
              >
                {isUploading ? "Uploading..." : "Continue to Verification"}
              </button>
            </form>
          </div>
        </div>
      </div>
      {/* File Info Dialog */}
      {showFileInfoDialog && fileInfo && (
        <FileInfoDialog
          isOpen={showFileInfoDialog}
          onClose={() => setShowFileInfoDialog(false)}
          fileInfo={fileInfo}
          processingOptions={processingOptions}
        />
      )}
    </div>
  );
}
